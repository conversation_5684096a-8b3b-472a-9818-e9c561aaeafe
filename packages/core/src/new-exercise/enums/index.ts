// 1=>单选  2=>多选  3=>填空  4=>判断  5=>解答
export enum QuestionAnswerMode {
  /* 单选 */
  SINGLE_CHOICE = 1,
  /* 多选 */
  MULTIPLE_CHOICE = 2,
  /* 填空 */
  FILL_IN_THE_BLANK = 3,
  /* 判断 */
  JUDGMENT = 4,
  /* 问答 */
  QA = 5,
}

/**
 * 反馈类型
 */
export enum AnswerFeedbackType {
  Correct = "correct",
  Incorrect = "incorrect",
  AnswerCarelessly = "answer_carelessly",
  ContinuousCorrect = "continuous_correct",
  DifficultyDown = "difficulty_down",
  DifficultyUp = "difficulty_up",

  // 下面两种不是后端返回的，是前端自己设置的
  Resume = "resume",
  GiveUp = "give_up",
}

/**
 * 题目渲染类型枚举
 * 用于确定前端如何渲染不同的题目类型
 */
export enum QuestionRenderType {
  /** 单选题 */
  SINGLE_CHOICE = "single_choice",
  /** 多选题 */
  MULTIPLE_CHOICE = "multiple_choice",
  /** 填空题 */
  FILL_BLANK = "fill_blank",
  /** 判断题 */
  JUDGMENT = "judgment",
  /** 问答题 */
  QA = "qa",
  /** 母子题（综合题） */
  PARENT_CHILD = "parent_child",
}

export enum EvaluationType {
  AUTO = 1,
  SELF = 2,
}

export enum SelfEvaluateType {
  // 待自评
  pending = 0,
  // 正确
  right = 1,
  wrong = 2,
  partial = 3,
}

// 回答内容的类型1:文本(默认), 2:图片, 3:视频，整个题目只有一个作答类型，不能混用
export enum InputModeType {
  KEYBOARD = 1,
  CAMERA = 2,
  VIDEO = 3,
  /* 白板 */
  WHITEBOARD = 5,
}
export enum AnswerVerifyType {
  UNANSWERED = 0, // 未作答
  CORRECT = 1, // 正确
  INCORRECT = 2, // 错误
  PARTIAL_CORRECT = 3, // 部分正确
  GIVE_UP = 4, // 放弃作答
  UNJUDGED = 99, // 未判题
}
