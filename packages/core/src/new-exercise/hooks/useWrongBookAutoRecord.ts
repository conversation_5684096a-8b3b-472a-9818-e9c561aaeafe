import { useEffect, useRef } from "react";
import type { CommonQuestion } from "@repo/core/types";
import { useExerciseContext } from "../context";
import { useWrongBookContextRequired } from "../context/wrong-book-context";
import { AnswerVerifyType } from "../enums";

/**
 * 错题本自动记录 Hook
 * 负责监听答题结果和预览模式，自动将题目加入错题本
 */
export function useWrongBookAutoRecord() {
  const { isPreview, questionStore } = useExerciseContext();
  const { lastSubmitResult, currentQuestion, questionStatus } = questionStore;
  const { recordQuestionAnswer } = useWrongBookContextRequired();

  // 🔑 使用 ref 记录已处理的答题结果，避免重复处理
  const processedResults = useRef(new Set<string>());

  // 🔧 预览模式下的错题本处理逻辑
  useEffect(() => {
    if (isPreview === true && questionStore.initialStudentAnswer.value) {
      const studentAnswer = questionStore.initialStudentAnswer.value;
      const currentQuestionId = questionStore.rootQuestion.value?.questionId;
      const currentQuestion = questionStore.rootQuestion.value;

      // 🔧 检查是否应该在错题本中（适配新数据结构）
      // 只使用当前题目的 exerciseStats（StudyQuestionInfo 中的字段）
      // 学生答案的 exerciseStats 代表下一题的作答结果，不应用于当前题判断
      const currentQuestionStats = currentQuestion?.exerciseStats;

      // 🔧 优先级：当前题目的 exerciseStats > 兜底字段
      const wrongTimes =
        currentQuestionStats?.wrongTimes ?? studentAnswer.wrongTimes;
      const inWrongNotebook = currentQuestionStats?.inWrongNotebook;

      // 🔧 优先使用 inWrongNotebook 字段，如果没有则根据 wrongTimes > 0 判断
      const shouldBeInWrongBook =
        inWrongNotebook !== undefined
          ? inWrongNotebook
          : typeof wrongTimes === "number" && wrongTimes > 0;

      if (
        shouldBeInWrongBook &&
        typeof currentQuestionId === "string" &&
        currentQuestionId.trim() !== "" &&
        typeof recordQuestionAnswer === "function"
      ) {
        recordQuestionAnswer(
          currentQuestionId,
          false, // isAnswerCorrect: false 表示答错
          wrongTimes,
          true, // isFinalAnswer: true
          false, // isGiveUp: false
          studentAnswer.evaluationType || 1, // verifyType
          typeof studentAnswer.answerResult === "number"
            ? studentAnswer.answerResult
            : 2 // answerVerify: 2 表示错误
        );
      }
    }
  }, [
    isPreview,
    questionStore.initialStudentAnswer.value,
    questionStore.rootQuestion.value,
    recordQuestionAnswer,
  ]);

  // 🆕 监听答题结果，自动记录到错题本
  useEffect(() => {
    const result = lastSubmitResult.value;
    const currentQuestionId = currentQuestion.value?.questionId;
    const rootQuestionId = questionStore.rootQuestion.value?.questionId;

    if (
      result &&
      Array.isArray(result.answerResult) &&
      isPreview !== true // 🔑 修复：只要不是明确的预览模式就处理
    ) {
      // 🔑 判断是否是放弃作答
      const isGiveUp = questionStatus.value === "giving_up";

      // 🔑 检查根题目ID是否有效
      if (typeof rootQuestionId !== "string" || rootQuestionId.trim() === "") {
        return; // 跳过无效的题目ID
      }

      // 🔑 检查答题结果是否与当前题目相关
      // 🔧 母子题修复：对于母子题，answerResult 中的 questionId 都是子题ID
      // 我们需要检查是否有任何子题ID在当前母题的子题列表中
      let hasMatchingResults = false;
      
      if (result.answerResult.length === 0) {
        // 放弃作答时 answerResult 可能为空，直接处理
        hasMatchingResults = true;
      } else {
        // 检查是否有匹配的子题ID
        const rootQuestion = questionStore.rootQuestion.value;
        if (
          rootQuestion?.subQuestionList &&
          rootQuestion.subQuestionList.length > 0
        ) {
          // 母子题：检查 answerResult 中的 questionId 是否在子题列表中
          const subQuestionIds = new Set<string>();
          const collectSubQuestionIds = (questions: CommonQuestion[]) => {
            questions.forEach((q) => {
              subQuestionIds.add(q.questionId);
              if (q.subQuestionList && q.subQuestionList.length > 0) {
                collectSubQuestionIds(q.subQuestionList);
              }
            });
          };
          collectSubQuestionIds(rootQuestion.subQuestionList);

          hasMatchingResults = result.answerResult.some((item) =>
            subQuestionIds.has(item.questionId)
          );
        } else {
          // 普通题：直接匹配题目ID
          hasMatchingResults = result.answerResult.some(
            (item) =>
              item.questionId === currentQuestionId ||
              item.questionId === rootQuestionId
          );
        }
      }

      if (!hasMatchingResults) {
        return; // 跳过不相关的答题结果
      }

      // 🔑 创建唯一标识符，避免重复处理同一个答题结果
      const resultKey = `${rootQuestionId}-${JSON.stringify({
        answerResults: result.answerResult.map((item) => ({
          questionId: item.questionId,
          answerVerify: item.answerVerify,
        })),
        wrongTimes: result.exerciseStats?.wrongTimes,
        isFinalAnswer: result.isFinalAnswer,
        isGiveUp,
      })}`;

      // 🔑 如果已经处理过这个结果，直接返回
      if (processedResults.current.has(resultKey)) {
        return;
      }

      // 🔑 标记为已处理
      processedResults.current.add(resultKey);

      // 🔧 母子题逻辑：判断是否有任何子题答错
      // 🔑 放弃作答时强制设为 true，确保加入错题本
      // 🔑 检查 answerVerify 是否为非正确状态（包括 GIVE_UP = 4）
      const hasAnyWrongAnswer =
        isGiveUp || result.answerResult.length === 0
          ? true
          : result.answerResult.some(
              (item) => item.answerVerify !== AnswerVerifyType.CORRECT
            );

      // 🆕 获取练习统计信息
      // 注意：result.exerciseStats 代表下一题的作答结果，不是当前题的
      const wrongTimes = result.exerciseStats?.wrongTimes;

      // 🆕 获取第一个答题结果的 verifyType 和 answerVerify 信息（用于记录）
      // 🔑 放弃作答时 answerResult 可能为空，使用默认值
      const firstAnswerResult = result.answerResult[0];
      const verifyType =
        typeof firstAnswerResult?.verifyType === "number"
          ? firstAnswerResult.verifyType
          : 1; // 默认系统判题
      const answerVerify = hasAnyWrongAnswer
        ? AnswerVerifyType.INCORRECT
        : AnswerVerifyType.CORRECT;

      // 🔧 错题本加入逻辑：
      // 1. 如果有任何子题答错了，无论母题当前是否在错题本中，都要加入错题本
      // 2. 如果所有子题都答对了，检查母题是否在错题本中：
      //    - 如果在错题本中，保持在错题本中（不移出）
      //    - 如果不在错题本中，不加入错题本

      // 获取母题的错题本状态
      const currentInWrongNotebook =
        questionStore.rootQuestion.value?.exerciseStats?.inWrongNotebook;

      const shouldForceAddToWrongBook = hasAnyWrongAnswer
        ? true // 有子题答错了：无论如何都加入错题本
        : currentInWrongNotebook === true; // 全部答对了：如果在错题本中则保持，否则不加入

      // 🔧 如果需要强制加入错题本，则覆盖 isCorrect 为 false
      const finalIsCorrect = shouldForceAddToWrongBook
        ? false
        : !hasAnyWrongAnswer;

      // 记录答题结果到错题本 ViewModel
      // 🔑 放弃作答时 finalIsCorrect 已经被设为 false，会正确加入错题本
      // 🔑 使用 rootQuestionId 作为记录的题目ID，因为错题本中记录的是母题
      recordQuestionAnswer(
        rootQuestionId,
        finalIsCorrect, // 🔧 使用修正后的 isCorrect 值
        wrongTimes,
        result.isFinalAnswer,
        isGiveUp, // 🔑 传入 isGiveUp 参数
        verifyType, // 🔑 传入 verifyType 参数
        answerVerify // 🔑 传入 answerVerify 参数
      );
    }
  }, [
    lastSubmitResult.value,
    currentQuestion.value?.questionId,
    questionStore.rootQuestion.value,
    questionStatus.value,
    isPreview,
    recordQuestionAnswer,
  ]);
}
