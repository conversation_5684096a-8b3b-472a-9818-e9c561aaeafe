/**
 * 策略模式基类 - 新版本
 * 专注于验证逻辑，不再处理答案构建
 */

import { CommonQuestion } from "@repo/core/types";
import type { ConfirmDialogConfigType } from "../components/exercise-dialog";
import { InputModeType, QuestionRenderType } from "../enums";
import type { Answer, QuestionStatus } from "../types/question";
import { SubQuestionMeta } from "../utils";

// ========== 简化的类型定义 ==========

export interface BeforeSubmitValidationResult {
  cancelSubmit?: boolean;
  canDirectSubmit: boolean;
  needsConfirmation: boolean;
  confirmConfig?: ConfirmDialogConfigType;
}

export interface SubmitValidationContext {
  questionStatus: QuestionStatus;
  userAnswerData: Map<string, Answer[]>;
  userAnswerTypeMap: Map<string, InputModeType>;
  questionMetaMap: Map<string, SubQuestionMeta>;
  isParentChild: boolean;
}

// 🗑️ 旧的提交相关接口已删除 - 现在使用统一的答案状态管理

// ========== 策略基类 ==========

export abstract class BaseQuestionStrategy {
  abstract readonly type: QuestionRenderType;
  abstract readonly name: string;
  abstract readonly description: string;

  // ========== 核心抽象方法 ==========

  /**
   * 提交前验证
   * @param question 题目信息
   * @param context 验证上下文
   * @returns 验证结果
   */
  abstract validateBeforeSubmit(
    question: CommonQuestion,
    context?: SubmitValidationContext
  ): BeforeSubmitValidationResult;
}

// ========== 工厂接口 ==========

export interface StrategyFactory {
  createStrategy(type: QuestionRenderType): BaseQuestionStrategy | null;
  registerStrategy(
    type: QuestionRenderType,
    strategyFactory: (type: QuestionRenderType) => BaseQuestionStrategy
  ): void;
  getSupportedTypes(): QuestionRenderType[];
}

export type QuestionStrategy = BaseQuestionStrategy;
// 🗑️ 删除 UserAnswerData 导出 - 已完全废弃
