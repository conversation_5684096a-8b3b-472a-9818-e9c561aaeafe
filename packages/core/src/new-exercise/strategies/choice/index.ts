/**
 * 选择型策略模块
 * 统一处理单选题、多选题、判断题的策略实现
 */

import { QuestionRenderType } from "@repo/core/new-exercise/enums";
import { CommonQuestion } from "@repo/core/types";
import { CONFIRM_DIALOG_CONFIGS } from "../../components/exercise-dialog";
import { getAnswerContentWithInputMode } from "../../utils/question-answer";
import {
  BaseQuestionStrategy,
  BeforeSubmitValidationResult,
  SubmitValidationContext,
} from "../base-strategy";

/**
 * 选择型策略类
 * 统一处理单选题、多选题、判断题的答案验证、格式化等逻辑
 */
export class SelectiveStrategy extends BaseQuestionStrategy {
  readonly type: QuestionRenderType = QuestionRenderType.SINGLE_CHOICE;
  readonly name: string;
  readonly description: string;

  constructor(type: QuestionRenderType) {
    super();
    this.type = type;

    // 根据题型设置名称和描述
    switch (type) {
      case QuestionRenderType.SINGLE_CHOICE:
        this.name = "单选题策略";
        this.description = "处理单选题的答案验证、格式化等逻辑";
        break;
      case QuestionRenderType.MULTIPLE_CHOICE:
        this.name = "多选题策略";
        this.description = "处理多选题的答案验证、格式化等逻辑";
        break;
      case QuestionRenderType.JUDGMENT:
        this.name = "判断题策略";
        this.description = "处理判断题的答案验证、格式化等逻辑";
        break;
      default:
        this.name = "选择型策略";
        this.description = "处理选择型题目的答案验证、格式化等逻辑";
    }
  }

  // ========== 核心方法实现 ==========

  validateBeforeSubmit(
    question: CommonQuestion,
    _context: SubmitValidationContext
  ): BeforeSubmitValidationResult {
    const { isParentChild } = _context || {};
    const answerData = _context?.userAnswerData;
    if (!answerData) {
      console.error("[SelectiveStrategy] 无法获取统一答案状态，阻止提交");
      return { canDirectSubmit: false, needsConfirmation: false };
    }

    // 题目下空位总数（无数据时至少按 1 位处理）
    const blanks = answerData.get(question.questionId) ?? [];
    const totalCount = blanks.length || 1;

    // 统一做一次解析：拿到每个空位有效选择数（去空格、去空串）
    const selectionsPerBlank: number[] = blanks.map((answer) => {
      const raw = getAnswerContentWithInputMode(answer) || "";
      if (!raw) return 0;
      const arr = raw
        .split(",")
        .map((s) => s.trim())
        .filter(Boolean);
      return arr.length;
    });

    // 已作答的空位数（有任意有效选择即算作答）
    const answeredCount = selectionsPerBlank.filter((n) => n > 0).length;

    // 1) 完全未作答
    if (totalCount === 0 || answeredCount === 0) {
      return {
        canDirectSubmit: false,
        needsConfirmation: true,
        confirmConfig: CONFIRM_DIALOG_CONFIGS.subjectiveEmpty,
      };
    }

    // 2) 部分未作答
    if (answeredCount < totalCount) {
      return {
        canDirectSubmit: false,
        needsConfirmation: true,
        confirmConfig: CONFIRM_DIALOG_CONFIGS.subjectivePartEmpty,
      };
    }

    // 3) 兜底：若存在空位解析后仍为 0（理论上不会走到这，因为 answeredCount==totalCount）
    if (selectionsPerBlank.some((n) => n === 0)) {
      return {
        canDirectSubmit: false,
        needsConfirmation: true,
        confirmConfig: CONFIRM_DIALOG_CONFIGS.subjectiveEmpty,
      };
    }

    // 4) 多选题特殊规则：任何一个空位只选了 1 个选项 -> 二次确认(母子题跳过)
    if (this.type === QuestionRenderType.MULTIPLE_CHOICE && !isParentChild) {
      if (selectionsPerBlank.some((n) => n === 1)) {
        return {
          canDirectSubmit: false,
          needsConfirmation: true,
          confirmConfig: CONFIRM_DIALOG_CONFIGS.multipleChoiceConfirm,
        };
      }
    }

    // 5) 通过
    return { canDirectSubmit: true, needsConfirmation: false };
  }
}
