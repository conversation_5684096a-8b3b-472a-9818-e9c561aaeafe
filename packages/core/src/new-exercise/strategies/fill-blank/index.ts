/**
 * 填空题策略模块
 * 处理填空题的答案验证、格式化等逻辑
 */

import { toast } from "@repo/core/components/stu-toast";
import { QuestionRenderType } from "@repo/core/new-exercise/enums";
import { CommonQuestion } from "@repo/core/types";
import { CONFIRM_DIALOG_CONFIGS } from "../../components/exercise-dialog";
import { isValidAnswerWithInputMode } from "../../utils/question-answer";
import {
  BaseQuestionStrategy,
  BeforeSubmitValidationResult,
  SubmitValidationContext,
} from "../base-strategy";

// ========== 填空题策略 ==========

/**
 * 填空题策略类
 * 处理填空题的答案验证、格式化等逻辑
 */
export class FillBlankStrategy extends BaseQuestionStrategy {
  readonly type: QuestionRenderType = QuestionRenderType.FILL_BLANK;
  readonly name = "填空题策略";
  readonly description = "处理填空题的答案验证、格式化等逻辑";

  // ========== 核心方法实现 ==========

  validateBeforeSubmit(
    question: CommonQuestion,
    _context: SubmitValidationContext
  ): BeforeSubmitValidationResult {
    // 从统一状态获取答案数据
    const answerData = _context?.userAnswerData;
    if (!answerData) {
      console.error("[FillBlankStrategy] 无法获取统一答案状态，阻止提交");
      return {
        canDirectSubmit: false,
        needsConfirmation: false,
      };
    }

    const totalCount =
      _context?.userAnswerData?.get(question.questionId)?.length || 1;

    const targetAnswers = answerData.get(question.questionId);

    let hasUploadPending = false;
    const answers =
      targetAnswers?.filter((answer) => {
        const { isValid, content, uploadPending } =
          isValidAnswerWithInputMode(answer);

        if (!isValid && uploadPending) {
          toast.info(uploadPending ? "请等待照片上传完成" : "");
          hasUploadPending = true;
        }
        return isValid || content.trim() !== "";
      }) || [];

    if (hasUploadPending) {
      return {
        cancelSubmit: true,
        canDirectSubmit: false,
        needsConfirmation: false,
      };
    }

    // 计算空答案数量，需要考虑不同输入模式的答案有效性,需要根据totalCount来计算
    const emptyCount = totalCount - answers.length;

    if (totalCount === 0 || emptyCount === totalCount) {
      // 一个都没有答（全部为空）
      return {
        canDirectSubmit: false,
        needsConfirmation: true,
        confirmConfig: CONFIRM_DIALOG_CONFIGS.subjectiveEmpty,
      };
    } else if (emptyCount > 0) {
      // 答案数量不够（还有填空未作答）
      return {
        canDirectSubmit: false,
        needsConfirmation: true,
        confirmConfig: CONFIRM_DIALOG_CONFIGS.subjectivePartEmpty,
      };
    }

    return {
      canDirectSubmit: true,
      needsConfirmation: false,
    };
  }
}
