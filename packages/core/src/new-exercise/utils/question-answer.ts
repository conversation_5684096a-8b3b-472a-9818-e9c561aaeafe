import { getInputModeOptions } from "../components/answer-area/InputModeSwitcher";
import {
  AnswerVerifyType,
  InputModeType,
  QuestionRenderType,
  SelfEvaluateType,
} from "../enums";
import { Answer, SubmitAnswerContent, WhiteBoardData } from "../types";
import { getBlankCountFast } from "./questionBlank";

export const VERIFY_TO_SELF_EVAL: Record<AnswerVerifyType, SelfEvaluateType> = {
  [AnswerVerifyType.CORRECT]: SelfEvaluateType.right,
  [AnswerVerifyType.INCORRECT]: SelfEvaluateType.wrong,
  [AnswerVerifyType.PARTIAL_CORRECT]: SelfEvaluateType.partial,
  [AnswerVerifyType.UNJUDGED]: SelfEvaluateType.pending,
  [AnswerVerifyType.GIVE_UP]: SelfEvaluateType.wrong,
  [AnswerVerifyType.UNANSWERED]: SelfEvaluateType.wrong,
};

export const getSelfEvalWithSystemVerify = (verify: AnswerVerifyType) => {
  return VERIFY_TO_SELF_EVAL[verify];
};

// 根据不同的作答类型获取实际的作答数据
export const getAnswerContentWithInputMode = (answer: Answer) => {
  // 根据输入模式从不同字段获取内容
  const { content } = isValidAnswerWithInputMode(answer);
  return content;
};

export const isValidAnswerWithInputMode = (answer: Answer) => {
  // 根据输入模式从不同字段获取内容
  let content = "";
  let isValid = false;
  let uploadPending = false;

  switch (answer.type) {
    case InputModeType.KEYBOARD:
    case InputModeType.VIDEO:
      // 键盘和视频模式：直接使用 content 字段
      content = answer.content || "";
      isValid = answer.content?.trim() !== "";
      break;

    case InputModeType.CAMERA:
      // 拍照模式：从 cameraData 获取图片URL，多张图片用逗号分隔
      if (answer?.cameraData && answer?.cameraData?.length > 0) {
        const hasPending = answer.cameraData.some(
          (item) => item.status == "pending"
        );

        content = answer.cameraData
          .filter((item) => item.status == "success")
          .map((item) => item.preview)
          .join(",");
        uploadPending = hasPending;
        isValid = hasPending ? false : true;
      } else {
        isValid = false;
        content = ""; // 兜底使用 content
      }
      break;

    case InputModeType.WHITEBOARD:
      // 白板模式：优先使用 whiteBoardData.imageUrl，兜底使用 content
      if (answer?.whiteBoardData) {
        content = answer.whiteBoardData.imageUrl || "";
        isValid = !!answer?.whiteBoardData.imageData;
      }
      break;

    default:
      // 默认情况：使用 content 字段
      content = answer.content || "";
      break;
  }
  return { content, isValid, uploadPending };
};

/** 构建提交数据 */
export const buildSubmissionData = ({
  isSelfEvaluation,
  userAnswerData,
  userAnswerTypeMap,
  isGiveUp,
}: {
  isSelfEvaluation?: boolean;
  userAnswerData: Map<string, Answer[]>;
  userAnswerTypeMap: Map<string, InputModeType>;
  isGiveUp?: boolean;
}): {
  studentAnswers: SubmitAnswerContent[];
  updateUserAnswerData: Map<string, Answer[]>;
} => {
  const updateUserAnswerDataMap = new Map<string, Answer[]>(userAnswerData);
  const submitList: SubmitAnswerContent[] = [];

  // 遍历所有题目的答案
  updateUserAnswerDataMap.forEach((answers, questionId) => {
    if (answers.length === 0) return;
    console.log(`answers`, answers);

    const questionRenderType = userAnswerTypeMap.get(questionId) || 1;

    const updateAnswerData: Answer[] = [];
    const newAnswers = answers.map((answer, index) => {
      const content = isGiveUp ? "" : getAnswerContentWithInputMode(answer);
      const newAnswer = {
        ...answer,
        content,
      };
      if (!content) {
        newAnswer.selfEvaluation = SelfEvaluateType.wrong;
      }
      updateAnswerData.push(newAnswer);

      return {
        content,
        index: answer.index,
        selfEvaluation: isSelfEvaluation ? answer.selfEvaluation : undefined,
      };
    });
    updateUserAnswerDataMap.set(questionId, updateAnswerData);
    submitList.push({
      questionId,
      type: questionRenderType,
      answers: newAnswers,
    });
  });

  return {
    studentAnswers: submitList,
    updateUserAnswerData: updateUserAnswerDataMap,
  };
};

export const isValidSelfEvaluation = (selfEvaluation?: SelfEvaluateType) => {
  return (
    selfEvaluation === SelfEvaluateType.right ||
    selfEvaluation === SelfEvaluateType.wrong ||
    selfEvaluation === SelfEvaluateType.partial
  );
};

export const hasPendingSelfEvaluation = (answers: Answer[]) => {
  return answers.some(
    (answer) =>
      answer.selfEvaluation == SelfEvaluateType.pending ||
      !answer.selfEvaluation
  );
};
export const buildInitAnswerData = ({
  answerContents,
  isAiCourseResume,
}: {
  answerContents: SubmitAnswerContent[];
  isAiCourseResume?: boolean;
}) => {
  let hasIncompleteSelfEvaluation = true;

  const initializedAnswerData = new Map<string, Answer[]>();

  answerContents?.map((item) => {
    const answers = item.answers?.map((answer) => {
      let finalSelfEvaluation = answer.selfEvaluation;

      if (!answer?.selfEvaluation) {
        const _answerResult = item.answerResult || answer.answerResult;
        const computedSelfEvaluation = getSelfEvalWithSystemVerify(
          _answerResult || AnswerVerifyType.UNANSWERED
        );

        hasIncompleteSelfEvaluation =
          _answerResult == AnswerVerifyType.UNJUDGED ||
          computedSelfEvaluation == SelfEvaluateType.pending;

        const selfEvaluation = hasIncompleteSelfEvaluation
          ? SelfEvaluateType.pending
          : computedSelfEvaluation;

        finalSelfEvaluation = selfEvaluation;
      }

      // 如果是在AI课程中恢复到自评状态，且没有自评，且没有作答，则设置为错误
      if (isAiCourseResume && !answer.selfEvaluation && !answer.content) {
        finalSelfEvaluation = SelfEvaluateType.wrong;
      }

      return {
        index: answer.index,
        type: item.type,
        content: answer.content,
        selfEvaluation: finalSelfEvaluation,
        answerResult: answer.answerResult,
      };
    });

    initializedAnswerData.set(item.questionId, answers);
  });

  return {
    initializedAnswerData,
    hasIncompleteSelfEvaluation,
  };
};

// 获取白板数据
export const getWhiteBoardData = (payload: {
  questionId: string;
  blankIndex: number;
  userAnswerDataMap: Map<string, Answer[]>;
}): WhiteBoardData | undefined => {
  const { questionId, blankIndex, userAnswerDataMap } = payload;
  const originData = userAnswerDataMap.get(questionId);
  if (!originData) return;
  return originData[blankIndex]?.whiteBoardData;
};

/**
 * 创建空答案数组（从 question-view-context 复制）
 */
export function createEmptyAnswers(data: {
  stem: string;
  answers: Answer[];
  isEnglish: boolean;
  questionRenderType: QuestionRenderType;
  isObjectiveQuestion: boolean;
}): Answer[] {
  const { stem, answers, isEnglish, questionRenderType, isObjectiveQuestion } =
    data;
  const count = getBlankCountFast(stem);
  // 如果用户没有回答，或者和一直 blank count 数量不符，则补齐空的作答到userAnswerDataMap.value中
  return Array.from({ length: count }, (_, index) => {
    if (!answers[index]) {
      const type = isObjectiveQuestion
        ? InputModeType.KEYBOARD
        : getInputModeOptions(
            isEnglish,
            questionRenderType === QuestionRenderType.QA
          )[0].value;

      return {
        content: "",
        selfEvaluation: SelfEvaluateType.wrong,
        index,
        type,
      };
    }
    return answers[index];
  });
}
