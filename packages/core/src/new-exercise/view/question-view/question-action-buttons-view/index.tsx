import { Signal } from "@preact-signals/safe-react";
import { memo } from "react";
import { useExerciseContext } from "../../../context";
import { useWrongBookAutoRecord } from "../../../hooks";
import { QuestionActionButtons } from "./question-action-buttons";
import { QuestionActionButtonsPreview } from "./question-action-buttons-preview";

/** 只在子组件中读取 signal，避免父组件订阅而重渲 */
export const QuestionActionButtonMask = memo(function QuestionActionButtonMask({
  atBottom,
}: {
  atBottom: Signal<boolean>;
}) {
  return (
    <div
      className="absolute bottom-0 right-0 z-[-1]"
      style={{
        width: "100%",
        height: "85%",
        borderRadius: "36px",
        background: !atBottom.value ? "var(--study-background)" : "transparent",
        filter: "blur(10px)",
      }}
    />
  );
});

const QuestionActionButtonsView = () => {
  const { isPreview } = useExerciseContext();

  // 🔧 使用专门的 Hook 处理错题本自动记录逻辑
  useWrongBookAutoRecord();

  return (
    <>
      {isPreview === true ? (
        <QuestionActionButtonsPreview />
      ) : (
        <QuestionActionButtons />
      )}
    </>
  );
};

export default QuestionActionButtonsView;
