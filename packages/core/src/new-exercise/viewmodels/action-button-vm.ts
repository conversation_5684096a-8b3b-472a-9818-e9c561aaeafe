import { useComputed } from "@preact-signals/safe-react";
import { StudyType } from "@repo/core/enums";
import { useDebouncedCallback } from "use-debounce";

import * as Sentry from "@sentry/nextjs";
import { useCallback, useMemo } from "react";
import { useExerciseContext } from "../context";
import { useQuestionViewContext } from "../context/question-view-context";
import { useWrongBookContextRequired } from "../context/wrong-book-context";
import { InputModeType } from "../enums";
import { useWrongQuestionBook } from "../hooks";
import { ApiGetNextQuestionData } from "../types";
import { getAnswerContentWithInputMode } from "../utils/question-answer";
import { useQuestionSubmissionVM } from "./submission-vm";
import { useSimpleWrongBookContinue } from "./wrong-book-continue-handler";

export function useActionButtonsVM() {
  const {
    questionStore,
    studyType,
    studySessionId: _studySessionId,
    handleContinueWithTransitions,
  } = useExerciseContext();

  // 🆕 获取导航API
  const {
    whiteboardRef: _whiteboardRef,
    saveWhiteboardData,
    checkAllWhiteboardData,
    isLastQuestion,
    isLastBlank,
  } = useQuestionViewContext();

  const {
    hasNextQuestion,
    questionStatus,
    isSubmitting,
    updateQuestionStatus,

    currentQuestion,
    canSubmitSelfEvaluation,
    userAnswerDataMap,
    currentInputMode,
    isParentChildQuestion,
    initialQuestionInfo,
    rootQuestion,
    isSubjectiveQuestion,
  } = questionStore;

  const { beforeSubmitAnswer, giveUpAnswer, handleSubmitSelfEvaluation } =
    useQuestionSubmissionVM();

  // 🆕 使用 Context 中的错题本 ViewModel，确保与其他组件使用同一个实例
  const wrongBookVm = useWrongBookContextRequired();
  const {
    getQuestionErrorReasonTags,
    getQuestionNotes,
    isInWrongQuestionBook,
    handleToggleWrongQuestionBook,
  } = wrongBookVm;

  const { questionId, wrongQuestionInfo } = currentQuestion.value ?? {};

  // 🔧 母子题修复：对于错题本操作，应该使用母题ID而不是子题ID
  const rootQuestionId = rootQuestion.value?.questionId;
  const questionIdForWrongBook = rootQuestionId || questionId;

  // 🆕 获取当前题目的错因标签和备注
  // 🔧 母子题修复：错因标签和备注也应该使用母题ID
  const currentErrorReasonTags =
    questionIdForWrongBook != null && questionIdForWrongBook.trim() !== ""
      ? getQuestionErrorReasonTags(questionIdForWrongBook)
      : [];
  const currentNotes =
    questionIdForWrongBook != null && questionIdForWrongBook.trim() !== ""
      ? getQuestionNotes(questionIdForWrongBook)
      : "";

  const handleSubmit = useDebouncedCallback(
    async () => {
      if (isSubmitting.value) return;
      isSubmitting.value = true;

      Sentry.addBreadcrumb({
        message: "提交按钮点击",
        data: {
          userAnswerDataMap: userAnswerDataMap.value,
        },
      });

      // 只有在提交时处于白板模式，才需要保存白板数据
      if (currentInputMode.value === InputModeType.WHITEBOARD) {
        await saveWhiteboardData(undefined, true);
      }
      // 提交前检查所有白板数据是否上传
      await checkAllWhiteboardData();
      console.log("提交前", userAnswerDataMap.value);

      await beforeSubmitAnswer();
    },
    400,
    { leading: true, trailing: false }
  );

  const handleGiveUpClick = useDebouncedCallback(
    async () => {
      if (isSubmitting.value) return;
      isSubmitting.value = true;

      Sentry.addBreadcrumb({
        message: "放弃作答按钮点击",
        data: {
          userAnswerDataMap: userAnswerDataMap.value,
        },
      });

      await giveUpAnswer();
    },
    400,
    { leading: true, trailing: false }
  );

  const handleUncertainClick = useCallback(async () => {
    // 🔥 修复：点击不确定时，先清空当前题目的用户答案，再更新状态
    // clearUserAnswer();
    updateQuestionStatus("uncertain");
  }, [updateQuestionStatus]);

  // 判断是否显示"放弃作答"按钮
  const shouldShowGiveUpButton = questionStatus.value === "uncertain";

  // 🆕 错题本相关逻辑 - 使用统一的 Hook
  // 🔧 母子题修复：使用母题ID进行错题本状态查询
  const {
    isCurrentInWrongBook,
    handleWrongQuestionBookAction,
    handleButtonClick,
  } = useWrongQuestionBook(
    questionIdForWrongBook, // 🔧 使用母题ID
    handleToggleWrongQuestionBook,
    isInWrongQuestionBook
  );

  // 🆕 判断是否应该显示"回到原题"
  const shouldShowBackToOriginal = useMemo(() => {
    // 只有在错题本模式下才考虑回到原题
    if (studyType !== StudyType.WRONG_QUESTION_BANK) {
      return false;
    }

    // 必须有相似题信息
    if (!wrongQuestionInfo?.recommendQuestionIds?.length) {
      return false;
    }

    // 🔑 关键判断：当前题目是相似题
    const isCurrentSimilarQuestion =
      wrongQuestionInfo.recommendQuestionIds.includes(questionId || "");

    if (!isCurrentSimilarQuestion) {
      return false;
    }

    // 🔑 判断是否是相似题序列中的最后一道题
    const currentSimilarIndex = wrongQuestionInfo.recommendQuestionIds.indexOf(
      questionId || ""
    );
    const isLastSimilarQuestion =
      currentSimilarIndex === wrongQuestionInfo.recommendQuestionIds.length - 1;

    // 只有当前是相似题且是最后一道相似题时，才显示"回到原题"
    return isLastSimilarQuestion;
  }, [studyType, wrongQuestionInfo?.recommendQuestionIds, questionId]);

  // 是否显示提交按钮
  const showSubmitButton = useComputed(() => {
    // 非母子题，且没有空位，则显示提交按钮
    if (!isParentChildQuestion.value && isLastBlank.value) {
      return true;
    }

    if (isParentChildQuestion.value && isLastQuestion.value) {
      return true;
    }

    return false;
  });

  /** 答案完整性 - 自动计算，避免重复计算 */
  const canSubmit = useComputed(() => {
    console.log(`canSubmit 1`, rootQuestion.value);
    // 直接从统一答案状态检查完整性
    const answers =
      userAnswerDataMap.value?.get(rootQuestion.value?.questionId || "") || [];

    if (!showSubmitButton.value) return false;
    if (isParentChildQuestion.value) {
      console.log(`canSubmit 2`, isParentChildQuestion.value);

      return true;
    }
    if (!isParentChildQuestion.value && isSubjectiveQuestion.value) {
      console.log(`canSubmit 3`, isSubjectiveQuestion.value);
      return true;
    }

    const isValid =
      answers.length > 0 &&
      answers.some((answer) => getAnswerContentWithInputMode(answer));
    console.log(`canSubmit 4`, isValid, answers);

    // 检查是否有有效答案
    return isValid;
  });

  // 🆕 错题本继续逻辑处理器
  const { handleContinueClick: handleContinueClickInWrongBook } =
    useSimpleWrongBookContinue({
      shouldShowBackToOriginal,
      onQuestionChange: (data: ApiGetNextQuestionData) => {
        initialQuestionInfo(data);
      },
      handleContinueWithTransitions,
    });

  const handleContinueClick = useCallback(async () => {
    if (shouldShowBackToOriginal) {
      await handleContinueClickInWrongBook();
    } else {
      await handleContinueWithTransitions();
    }
  }, [
    shouldShowBackToOriginal,
    handleContinueClickInWrongBook,
    handleContinueWithTransitions,
  ]);

  // 获取继续按钮的文案
  const getContinueButtonText = useComputed(() => {
    // 🆕 如果应该回到原题，显示"回到原题"
    if (shouldShowBackToOriginal) {
      return "回到原题";
    }

    // 如果没有下一题且不是AI课类型，显示"查看报告"
    if (!hasNextQuestion.value && studyType !== StudyType.AI_COURSE) {
      return "查看报告";
    }
    return studyType === StudyType.WRONG_QUESTION_BANK ? "下一题" : "继续";
  });

  const handleSubmitSelfEvaluationDebounce = useDebouncedCallback(
    handleSubmitSelfEvaluation,
    400,
    { leading: true, trailing: false }
  );

  return {
    showSubmitButton,
    shouldShowGiveUpButton,
    canSubmit,
    handleContinueClick,
    getContinueButtonText,
    handleSubmit,
    handleGiveUpClick,
    handleUncertainClick,
    handleWrongQuestionBookAction,
    handleButtonClick,
    handleSubmitSelfEvaluation: handleSubmitSelfEvaluationDebounce,
    isCurrentInWrongBook,
    currentErrorReasonTags,
    currentNotes,
    wrongBookVm,
    isSubmitting,
    canSubmitSelfEvaluation,
    onToggleWrongQuestionBook: handleToggleWrongQuestionBook,
  };
}
