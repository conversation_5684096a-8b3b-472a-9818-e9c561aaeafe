import {
  untracked,
  useComputed,
  useSignal,
  useSignalEffect,
  type Signal,
} from "@preact-signals/safe-react";
import { useRef } from "react";
import { IOption, ItemStatus } from "../components";
import { useExerciseContext } from "../context";
import { useQuestionViewContext } from "../context/question-view-context";
import { AnswerVerifyType } from "../enums";
import { getTreeOptionForPath } from "../utils";
import { isValidAnswerWithInputMode } from "../utils/question-answer";

export interface QuestionNavVM {
  /** 导航项（含 status），供视图渲染。只读 Signal，请在子组件里读取 .value */
  navOptions: Signal<IOption[]>;
}
/** 4) 判分结果 -> UI 状态映射（使用项目内枚举） */
const statusFromVerify: Record<AnswerVerifyType, ItemStatus> = {
  [AnswerVerifyType.CORRECT]: ItemStatus.RIGHT,
  [AnswerVerifyType.INCORRECT]: ItemStatus.WRONG,
  [AnswerVerifyType.PARTIAL_CORRECT]: ItemStatus.PARTIAL,
  [AnswerVerifyType.UNANSWERED]: ItemStatus.WRONG,
  [AnswerVerifyType.GIVE_UP]: ItemStatus.WRONG,
  [AnswerVerifyType.UNJUDGED]: ItemStatus.WRONG,
};

/**
 * 统一管理“是否作答/对错状态”的导航 VM：
 * - 监听 answers/verifyMap 等信号
 * - 仅在状态变化时，用 rAF 合并调用 updateTreeOptions
 * - 视图层只渲染 navOptions，不直接操作状态
 */
export function useQuestionNavVM(): QuestionNavVM {
  const { questionStore } = useExerciseContext();
  const {
    isParentChildQuestion,
    currentQuestionAnswers,
    activeBlankIndex,
    currentQuestion,
    isAnswerCompletedStatus,
    answerVerifMap,
    activeQuestionIndex,
  } = questionStore;

  const { updateTreeOptions, treeOptions } = useQuestionViewContext();

  /** 1) 派生：当前“是否已经作答” */
  const isAnswered = useComputed(() => {
    const answers = currentQuestionAnswers.value;
    if (!answers) return false;

    if (isParentChildQuestion.value) {
      return answers.some((a) => isValidAnswerWithInputMode(a).isValid);
    } else {
      const idx = activeBlankIndex.value;
      const a = answers[idx];
      return !!a && isValidAnswerWithInputMode(a).isValid;
    }
  });

  /** 2) 目标 UI 状态：已作答/未作答 */
  const desiredAnsweredStatus = useComputed<ItemStatus>(() =>
    isAnswered.value ? ItemStatus.ANSWERED : ItemStatus.DEFAULT
  );

  /** 3) rAF 去抖：已作答状态对齐 */
  const rafAnsweredRef = useRef<number | null>(null);

  useSignalEffect(() => {
    const list = treeOptions.value;
    if (!list?.length) return;

    const idx = activeQuestionIndex.value ?? 0;

    const next = desiredAnsweredStatus.value;
    const prev = getTreeOptionForPath(list, idx)?.status;
    if (prev === next) return;

    if (rafAnsweredRef.current != null) {
      cancelAnimationFrame(rafAnsweredRef.current);
      rafAnsweredRef.current = null;
    }

    rafAnsweredRef.current = requestAnimationFrame(() => {
      rafAnsweredRef.current = null;
      const targetId = untracked(() => currentQuestion.value?.questionId);
      const isParent = untracked(() => isParentChildQuestion.value);

      if (isParent) {
        updateTreeOptions({ targetId, item: { status: next } });
      } else {
        updateTreeOptions({
          targetId: currentQuestion.value?.questionId,
          item: { status: next },
        });
      }
    });
  });

  /** 5) rAF 去抖：已判分后，批量对齐“对/错/部分对” */
  const rafVerifyRef = useRef<number | null>(null);

  useSignalEffect(() => {
    const verifiedMap = answerVerifMap.value; // Map<questionId, { answerVerify: number }>
    if (!verifiedMap?.size) return;
    if (!isAnswerCompletedStatus.value) return;

    const list = treeOptions.value;
    if (!list?.length) return;

    // 构建当前状态索引：id -> status（字段名可能是 id 或 questionId）
    const currentStatusById = new Map<string, ItemStatus>();
    for (const it of list as Array<{
      id?: string;
      questionId?: string;
      status: ItemStatus;
    }>) {
      const id = (it.id ?? (it as any).questionId) as string | undefined;
      if (id) currentStatusById.set(id, it.status);
    }

    // 计算需要更新的条目
    const updates: Array<{ targetId: string; status: ItemStatus }> = [];
    verifiedMap.forEach((val, key: string) => {
      const desired =
        statusFromVerify[
          (val?.answerVerify as AnswerVerifyType) ?? AnswerVerifyType.UNJUDGED
        ] ?? ItemStatus.DEFAULT;
      const prev = currentStatusById.get(key);
      if (prev !== desired) updates.push({ targetId: key, status: desired });
    });

    if (updates.length === 0) return;

    if (rafVerifyRef.current != null) {
      cancelAnimationFrame(rafVerifyRef.current);
      rafVerifyRef.current = null;
    }

    rafVerifyRef.current = requestAnimationFrame(() => {
      rafVerifyRef.current = null;
      for (const u of updates) {
        updateTreeOptions({
          targetId: u.targetId,
          item: { status: u.status },
        });
      }
    });
  });

  /** 对外暴露：只读导航列表（给视图渲染即可） */
  const navOptions = useSignal(treeOptions.value);
  // 同步底层 treeOptions 改变（引用可能变）
  useSignalEffect(() => {
    if (navOptions.value !== treeOptions.value) {
      navOptions.value = treeOptions.value;
    }
  });

  return { navOptions };
}
