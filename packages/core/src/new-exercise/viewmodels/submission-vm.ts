/**
 * 题目提交 Hook（Context-First 零参数架构）
 *
 * 架构设计：
 * - Hook：通过 Context 获取所有数据，负责业务逻辑协调
 * - Context：提供统一的数据源和方法
 * - 策略类：处理题型特定的校验规则、数据转换
 *
 * 架构优势：
 * - 零参数传递：Hook 通过 Context 获取所有数据
 * - 类型安全：Context 提供完整类型定义
 * - 性能优化：Context 已使用 useMemo 优化
 * - 符合 React 最佳实践：正确使用 Context 模式
 */

import { useCallback } from "react";
import { useExerciseContext } from "../context/exercise-context";
import type {
  AnswerResult,
  QuestionStatus,
  SubmitAnswerContent,
  SubmitAnswerResponse,
} from "../types/question";
import {
  isChoiceQuestionType,
  isMultipleChoiceQuestionType,
  isObjectiveQuestionType,
  isSelfEvaluationQuestionType,
  isSubjectiveQuestionType,
} from "../utils/question-is";
// StudyType 已在 Signal store 中处理
import { useComputed } from "@preact-signals/safe-react";
import { StudyType } from "@repo/core/enums";
import * as Sentry from "@sentry/nextjs";
import { HandleProgressParams, useProgressBar } from "../components";
import { AnswerVerifyType } from "../enums";
import { useClient } from "../hooks";
import { useSubmitStudyAnswer } from "../models/exercise-model";
import {
  buildSubmissionData,
  getSelfEvalWithSystemVerify,
} from "../utils/question-answer";
import { useConfirmDialogVM } from "./confirm-vm";

// ✅ Context-First 架构：零参数接口
// Hook 通过 useExerciseContext() 获取所有必要数据

export interface SubmissionResult {
  success: boolean;
  cancelled?: boolean;
  error?: Error;
  result?: SubmitAnswerResponse; // API 返回结果
}

type ComputeNextStatusArgs = {
  prevStatus: QuestionStatus;
  isGiveUp: boolean;
  isChoice: boolean;
  isCorrect: boolean;
  isFinalAnswer?: boolean | null;
  hasSelfEval: boolean;
  isParent: boolean;
};

const getNextStatus = ({
  prevStatus,
  isGiveUp,
  isChoice,
  isCorrect,
  isFinalAnswer,
  hasSelfEval,
  isParent,
}: ComputeNextStatusArgs): QuestionStatus => {
  const isAnswering = prevStatus === "answering" || prevStatus === "uncertain";
  // 1) 放弃优先级最高
  if (isGiveUp) return "giving_up";

  // 2) 二次作答提交后，无论正确与否都结束
  if (prevStatus === "second_answering") return "submitted";

  // 3) 🆕 自评题：使用聚合判断，首次作答后进入评估
  if (hasSelfEval && isAnswering) {
    return "evaluating";
  }

  // 4) 🆕 选择题：首次作答错误且允许再次作答 → 进入二次作答（母子题禁用二次作答）
  if (
    isChoice &&
    !isParent && // 🆕 母子题禁用二次作答
    isAnswering &&
    !isCorrect &&
    isFinalAnswer === false
  ) {
    return "second_answering";
  }

  // 5) 其他情况直接提交完成
  return "submitted";
};

/**
 * ✅ Context-First 题目提交 Hook（零参数架构）
 *
 * 通过 useExerciseContext() 获取所有必要数据：
 * - 题目信息、状态、用户答案
 * - ViewModel 实例和方法
 * - 提交相关的配置和回调
 */
export function useQuestionSubmissionVM() {
  // ✅ 通过 Context 获取所有必要数据
  const {
    questionStore,
    studyType,
    studySessionId: contextStudySessionId,
    widgetIndex,
    trackEventWithExercise,
    onQuestionChange,
  } = useExerciseContext();

  const { surveillanceReport } = useClient();

  // 🔑 动态获取最新的 studySessionId（从 URL 参数中获取，因为 enterSession 会更新 URL）
  // 这确保了在错题本模式下能够使用正确的 studySessionId
  const urlParams =
    typeof window !== "undefined"
      ? new URLSearchParams(window.location.search)
      : null;
  const actualStudySessionId = urlParams?.get("studySessionId");
  const studySessionId = actualStudySessionId
    ? parseInt(actualStudySessionId, 10)
    : contextStudySessionId || 0;

  const {
    timerState,
    currentStrategy,
    progressBarState,
    isSubmitting,
    questionStatus,
    currentQuestion,
    updateQuestionStatus,
    questionRenderType,
    lastSubmitResult,
    streakCount,
    initialProgress,
    isChoiceQuestion,
    userAnswerDataMap,
    userAnswerTypeMap,
    rootQuestion,
    canSubmitSelfEvaluation,
    hasAnySelfEvalInWholeQuestion,
    isParentChildQuestion,
    updateAnswerVerifMap,
    clearUserAnswer,
    updateSelfEvaluation,
    allQuestionMetaMap,
  } = questionStore;

  /** 提交API参数 - 🔥 修复：使用函数而非计算属性，避免频繁重新计算导致循环依赖 */
  const submitApiParams = useComputed(() => ({
    studySessionId: studySessionId || 0,
    questionId: rootQuestion.value?.questionId || "",
    answerDuration: timerState.value.currentTime,
    ...((studyType || StudyType.AI_COURSE) === StudyType.AI_COURSE &&
      widgetIndex !== undefined && {
        widgetIndex: widgetIndex,
      }),
  }));

  const { submitAnswer: fetchSubmitAnswer } = useSubmitStudyAnswer();

  // 🔥 使用 useProgressBar hook 统一管理进度条状态和动画
  const { handleProgress } = useProgressBar({
    initialProgress: initialProgress.value,
  });
  // currentQuestion 和 questionStatus 从 context 获取以保持兼容性

  // 🔧 使用弹窗 Hook
  const { confirmSubmit } = useConfirmDialogVM();

  const handleQuestionProgress = useCallback(
    (
      result: SubmitAnswerResponse,
      questionStatus: QuestionStatus | "initial"
    ) => {
      // 更新进度,在已提交、放弃作答、首次错误时更新进度
      const canUpdateProgress = [
        "submitted",
        "giving_up",
        "second_answering",
        "initial",
      ].includes(questionStatus);
      if (!canUpdateProgress) return;
      // 根据答题结果更新前端进度，首次错误只播放动画，不更新进度
      const currentProgress =
        questionStatus == "second_answering"
          ? progressBarState.value.progress
          : result?.progressInfo?.currentProgress || 0;

      // 检测是否为最后一题，如果是则立即设置进度条到100%
      const isLastQuestion = !result?.hasNextQuestion;

      // 判断答题结果，用于动画效果
      const isCorrect =
        result?.answerResult?.length &&
        result?.answerResult?.every(
          (item) => item.answerVerify === AnswerVerifyType.CORRECT
        );

      // 进度条上面的文字
      const explosionText = result?.feedback?.content || "";

      // 如果最后一题，直接设置为100%，避免时序竞争导致进度条未满
      const progressPercentage = isLastQuestion ? 100 : currentProgress;

      let type: HandleProgressParams["type"] = isCorrect
        ? "correct"
        : "incorrect";
      if (questionStatus === "initial") {
        type = "default";
      }

      progressBarState.value = handleProgress({
        type: type,
        text: explosionText,
        progress: progressPercentage,
        correctComboCount: result?.correctComboCount || 0,
        isAllCorrect: isLastQuestion && result.allCorrect,
      });
    },
    [handleProgress, progressBarState]
  );

  const updateSelfEvaluateWithSystemVerify = useCallback(
    (answerResult: AnswerResult[]) => {
      // 遍历更新selfEvaluation,调用 updateSelfEvaluation 方法
      answerResult.forEach((item) => {
        item.evaluations.forEach((evaluation, index) => {
          updateSelfEvaluation({
            questionId: item.questionId,
            blankIndex: index,
            selfEvaluation: getSelfEvalWithSystemVerify(
              evaluation.answerVerify
            ),
          });
        });
      });
    },
    [updateSelfEvaluation]
  );

  const afterSubmitAnswer = useCallback(
    ({
      result,
      isGiveUp,
      studentAnswers,
      isSubmitSelfEvaluation,
    }: {
      result: SubmitAnswerResponse;
      isGiveUp: boolean;
      studentAnswers: SubmitAnswerContent[];
      isSubmitSelfEvaluation?: boolean;
    }) => {
      // 解构 + 明确默认值
      const {
        answerResult = [],
        isFinalAnswer,
        correctComboCount = 0,
      } = result;
      updateAnswerVerifMap(answerResult || []);

      // 判定正确
      const isCorrect =
        answerResult?.length > 0 &&
        answerResult?.every(
          (item) => item.answerVerify === AnswerVerifyType.CORRECT
        );

      // 🔍 调试信息：状态转换参数
      console.log("🔍 状态转换参数:", {
        prevStatus: questionStatus.value,
        isGiveUp,
        isChoice: isChoiceQuestion.value,
        isCorrect,
        isFinalAnswer,
        hasSelfEval: hasAnySelfEvalInWholeQuestion.value,
        isParent: isParentChildQuestion.value,
      });

      // 计算下一状态（纯函数，便于读懂与测试）
      const nextState = getNextStatus({
        prevStatus: questionStatus.value,
        isGiveUp,
        isChoice: isChoiceQuestion.value,
        isCorrect,
        isFinalAnswer,
        hasSelfEval: hasAnySelfEvalInWholeQuestion.value,
        isParent: isParentChildQuestion.value,
      });

      console.log("🔍 计算出的下一状态:", nextState);

      // 更新本地状态
      lastSubmitResult.value = result;
      streakCount.value = correctComboCount;

      // 同步题目状态与进度
      console.log("🔍 更新题目状态:", nextState);
      updateQuestionStatus(nextState);
      handleQuestionProgress(result, nextState);

      // 🔧 题目状态变化时也触发 onQuestionChange，确保外部组件能响应状态变化
      if (onQuestionChange && rootQuestion.value) {
        console.log("🔧 触发 onQuestionChange，状态:", nextState);
        onQuestionChange({
          questionData: rootQuestion.value,
          index: 0, // 当前题目索引
          questionStatus: nextState,
          questionType: {
            isObjective: isObjectiveQuestionType(rootQuestion.value),
            isSubjective: isSubjectiveQuestionType(rootQuestion.value),
            isChoice: isChoiceQuestionType(rootQuestion.value),
            isMultipleChoice: isMultipleChoiceQuestionType(rootQuestion.value),
            isSelfEvaluation: isSelfEvaluationQuestionType(rootQuestion.value),
          },
        });
      }
      if (nextState === "submitted" && !hasAnySelfEvalInWholeQuestion.value) {
        updateSelfEvaluateWithSystemVerify(answerResult);
      }

      if (isGiveUp) {
        clearUserAnswer(rootQuestion.value?.questionId);
        surveillanceReport({
          operationType: 5,
          questionId: rootQuestion.value?.questionId,
          questionType: rootQuestion.value?.questionType ?? undefined,
          questionAnswerMode:
            rootQuestion.value?.questionAnswerMode ?? undefined,
          isCorrect: false,
          answerTime: timerState.value.currentTime,
          correctStreak: result.correctComboCount,
          maxCorrectComboCount: result.maxCorrectComboCount,
          totalQuestions: result.progressInfo?.totalQuestions,
          answerProgress: result.progressInfo?.currentProgress,
        });
      } else {
        surveillanceReport({
          operationType: 5,
          questionId: rootQuestion.value?.questionId,
          questionType: rootQuestion.value?.questionType ?? undefined,
          questionAnswerMode:
            rootQuestion.value?.questionAnswerMode ?? undefined,
          isCorrect,
          answerTime: timerState.value.currentTime,
          correctStreak: result.correctComboCount,
          maxCorrectComboCount: result.maxCorrectComboCount,
          totalQuestions: result.progressInfo?.totalQuestions,
          answerProgress: result.progressInfo?.currentProgress,
        });
      }

      // 埋点
      trackEventWithExercise("exercise_answer_submit", {
        answer_content: studentAnswers,
        is_giveup: isGiveUp,
        answer_duration: submitApiParams.peek().answerDuration,
        isSubmitSelfEvaluation,
      });
    },
    [
      // ✅ 建议依赖“容器”而不是 .value 本身（确保引用稳定）
      questionStatus,
      isChoiceQuestion,
      lastSubmitResult,
      streakCount,
      updateQuestionStatus,
      handleQuestionProgress,
      submitApiParams,
      trackEventWithExercise,
      updateAnswerVerifMap,
      clearUserAnswer,
      rootQuestion,
      updateSelfEvaluateWithSystemVerify,
      hasAnySelfEvalInWholeQuestion,
      isParentChildQuestion,
      surveillanceReport,
      timerState,
      onQuestionChange,
    ]
  );

  const submitAnswer = useCallback(
    async (isGiveUp = false): Promise<SubmissionResult> => {
      try {
        // 使用新的统一格式构建答案数据
        const { studentAnswers, updateUserAnswerData } = buildSubmissionData({
          isGiveUp,
          userAnswerData: userAnswerDataMap.value,
          userAnswerTypeMap: userAnswerTypeMap.value,
        });
        if (!rootQuestion.value?.questionId) {
          return { success: false, error: new Error("题目ID不存在") };
        }

        isSubmitting.value = true;
        const result = await fetchSubmitAnswer({
          ...submitApiParams.value, // 包含 studySessionId, questionId, answerDuration, widgetIndex
          questionId: rootQuestion.value?.questionId || "", // 确保类型正确
          studentAnswers: isGiveUp ? [] : studentAnswers,
          isGiveup: isGiveUp, // 🔥 使用传入的放弃标识
        });
        isSubmitting.value = false;

        if (updateUserAnswerData) {
          userAnswerDataMap.value = updateUserAnswerData;
        }
        if (result) {
          afterSubmitAnswer({ result, isGiveUp, studentAnswers });
        }

        return { success: true, result };
      } catch (error) {
        isSubmitting.value = false;
        Sentry.captureException(error, {
          level: "fatal",
          extra: {
            message: "submitAnswer 提交失败",
            question: rootQuestion.value,
            userAnswerDataMap: Object.fromEntries(userAnswerDataMap.value),
            allQuestionMetaMap: Object.fromEntries(allQuestionMetaMap.value),
            lastSubmitResult: lastSubmitResult.value,
          },
        });
        return {
          success: false,
          error: error instanceof Error ? error : new Error(String(error)),
        };
      }
    },
    [
      userAnswerTypeMap,
      fetchSubmitAnswer,
      rootQuestion,
      afterSubmitAnswer,
      submitApiParams,
      isSubmitting,
      userAnswerDataMap,
      lastSubmitResult,
      allQuestionMetaMap,
    ]
  );
  /**
   * ✅ Hook 完全主导：拥有所有业务逻辑
   *
   * 完整的业务逻辑流程：
   * 1. 策略调用和校验
   * 2. 弹窗处理
   * 3. API 调用
   * 4. 状态更新（questionState 计算）
   * 5. 埋点
   * 6. 进度处理
   */
  const beforeSubmitAnswer = async (): Promise<SubmissionResult> => {
    try {
      if (!rootQuestion.value) {
        return { success: false, error: new Error("当前题目信息不存在") };
      }

      if (!rootQuestion.value || !currentStrategy.value) {
        return {
          success: false,
          error: new Error(`未找到题型 ${questionRenderType} 对应的策略`),
        };
      }

      const strategyContext = {
        questionStatus: questionStatus.value,
        userAnswerData: userAnswerDataMap.value,
        questionMetaMap: allQuestionMetaMap.value,
        isParentChild: isParentChildQuestion.value,
        userAnswerTypeMap: userAnswerTypeMap.value,
      };

      // 策略验证从统一状态获取数据，不再需要 UserAnswerData
      const validation = currentStrategy.value.validateBeforeSubmit(
        rootQuestion.value,
        strategyContext
      );

      if (validation.cancelSubmit) {
        isSubmitting.value = false;
        return { success: false, cancelled: true };
      }

      // 3. ✅ 弹窗处理
      if (validation.needsConfirmation) {
        isSubmitting.value = false;

        // 显示确认弹窗，等待用户确认
        const confirmed = await confirmSubmit(validation.confirmConfig);

        if (!confirmed) {
          // 用户取消提交
          return { success: false, cancelled: true };
        }
      }

      await submitAnswer();

      return { success: true, result: undefined };
    } catch (error) {
      console.error("[useQuestionSubmissionVM] 提交失败:", error);

      Sentry.captureException(error, {
        level: "fatal",
        extra: {
          message: "beforeSubmitAnswer 提交失败",
          question: rootQuestion.value,
          userAnswerDataMap: Object.fromEntries(userAnswerDataMap.value),
          allQuestionMetaMap: Object.fromEntries(allQuestionMetaMap.value),
          lastSubmitResult: lastSubmitResult.value,
        },
      });

      return {
        success: false,
        error: error instanceof Error ? error : new Error(String(error)),
      };
    }
  };

  /**
   * ✅ 放弃作答专用方法
   *
   * 使用空答案数据提交，标记为放弃作答
   */
  const giveUpAnswer = useCallback(async (): Promise<SubmissionResult> => {
    const res = await submitAnswer(true).finally(() => {
      isSubmitting.value = false;
    }); // 标记为放弃
    return res;
  }, [submitAnswer, isSubmitting]);

  /**
   * ✅ 提交自评专用方法
   *
   * 从 Signal Store 中获取自评数据并提交
   */
  const handleSubmitSelfEvaluation = useCallback(async () => {
    try {
      if (!canSubmitSelfEvaluation.value) {
        return {
          success: false,
          error: new Error("自评数据未完成，无法提交"),
        };
      }

      // 3. 🔧 使用新的统一格式构建包含自评结果的答案数据
      // 统一答案状态已经包含了自评结果，直接构建提交数据
      const { studentAnswers } = buildSubmissionData({
        isSelfEvaluation: true,
        userAnswerData: userAnswerDataMap.value,
        userAnswerTypeMap: userAnswerTypeMap.value,
      });

      // 4. 🔧 直接调用 fetchSubmitAnswer，绕过 submitAnswer 的数据构建逻辑
      if (!rootQuestion.value?.questionId) {
        return { success: false, error: new Error("题目ID不存在") };
      }

      const result = await fetchSubmitAnswer({
        ...submitApiParams.value, // 包含 studySessionId, questionId, answerDuration, widgetIndex
        questionId: rootQuestion.value.questionId,
        studentAnswers: studentAnswers, // 🔧 使用新格式数据（已包含自评结果）
        isGiveup: false,
      });

      if (result) {
        afterSubmitAnswer({
          result,
          isGiveUp: false,
          studentAnswers,
          isSubmitSelfEvaluation: true,
        });
      }

      return { success: true, result };
    } catch (error) {
      console.error("[handleSubmitSelfEvaluation] 自评提交失败:", error);
      Sentry.captureException(error, {
        level: "fatal",
        extra: {
          message: "handleSubmitSelfEvaluation 自评提交失败",
          question: rootQuestion.value,
          userAnswerDataMap: Object.fromEntries(userAnswerDataMap.value),
          allQuestionMetaMap: Object.fromEntries(allQuestionMetaMap.value),
          lastSubmitResult: lastSubmitResult.value,
        },
      });
      return {
        success: false,
        error: error instanceof Error ? error : new Error(String(error)),
      };
    }
  }, [
    afterSubmitAnswer,
    canSubmitSelfEvaluation,
    fetchSubmitAnswer,
    submitApiParams,
    rootQuestion,
    userAnswerDataMap,
    userAnswerTypeMap.value,
    lastSubmitResult,
    allQuestionMetaMap,
  ]);

  // ✅ 返回简化的接口
  return {
    canSubmitSelfEvaluation,
    beforeSubmitAnswer,
    submitAnswer,
    giveUpAnswer,
    handleSubmitSelfEvaluation,
  };
}
